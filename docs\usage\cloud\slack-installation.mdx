---
title: Slack Integration (Beta)
description: This guide walks you through installing the OpenHands Slack app.
---

## Prerequisites

- Access to OpenHands Cloud

## Installation Steps

<AccordionGroup>
<Accordion title="Install Slack App (only for Slack admins/owners)">

  **This step is for Slack admins/owners**

  1. Make sure you have permissions to install Apps to your workspace.
  2. Click the button below to install OpenHands Slack App <a target="_blank" href="https://slack.com/oauth/v2/authorize?client_id=7477886716822.8729519890534&scope=app_mentions:read,chat:write,users:read,channels:history,groups:history,mpim:history,im:history&user_scope=channels:history,groups:history,im:history,mpim:history"><img alt="Add to Slack" height="40" width="139" src="https://platform.slack-edge.com/img/add_to_slack.png" srcSet="https://platform.slack-edge.com/img/add_to_slack.png 1x, https://platform.slack-edge.com/img/<EMAIL> 2x" /></a>
  3. In the top right corner, select the workspace to install the OpenHands Slack app.
  4. Review permissions and click allow.

</Accordion>

<Accordion title="Authorize Slack App (for all Slack workspace members)">

  **Make sure your Slack workspace admin/owner has installed OpenHands Slack App first**

  Every user in the slack workspace (including admins/owners) must link their Cloud OpenHands account to the OpenHands Slack App. To do this
  1. Visit [integrations settings](https://app.all-hands.dev/settings/integrations) in OpenHands Cloud.
  2. Click the button "Install Slack App".
  3. In the top right corner, select the workspace to install the OpenHands Slack app.
  4. Review permissions and click allow.

  Depending on the workspace settings, you may need approval from your slack admin to authorize the Slack App.

</Accordion>

</AccordionGroup>


## Working With the Slack App

To start a new conversation, you can mention `@openhands` in a new message or a thread inside any Slack channel.

Once a conversation is started, all thread messages underneath it will be follow-up messages to OpenHands.

To send follow-up messages for the same conversation, mention `@openhands` in a thread reply to the original message. You must be the user who started the conversation.

## Example conversation

### Start a new conversation, and select repo

Conversation is started by mentioning `@openhands`.

![slack-create-convo.png](/static/img/slack-create-convo.png)

### See agent response and send follow up messages

Initial request is followed up by mentioning `@openhands` in a thread reply.

![slack-results-and-follow-up.png](/static/img/slack-results-and-follow-up.png)

## Pro tip

You can mention a repo name when starting a new conversation in the following formats

1. "My-Repo" repo (e.g `@openhands in the openhands repo ...`)
2. "All-Hands-AI/OpenHands" (e.g `@openhands in All-Hands-AI/OpenHands ...`)

The repo match is case insensitive. If a repo name match is made, it will kick off the conversation.
If the repo name partially matches against, multiple repos, you'll be asked to select a repo from the filtered list.

![slack-pro-tip.png](/static/img/slack-pro-tip.png)
